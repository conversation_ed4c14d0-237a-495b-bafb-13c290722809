import { z } from 'zod';
import { validateEmail } from '../rules/email';

/**
 * Schema para validação de email
 */
export const emailSchema = z
  .string()
  .min(1, 'Email é obrigatório')
  .refine(validateEmail, 'Email inválido');

/**
 * Schema para validação de senha
 */
export const passwordSchema = z
  .string()
  .min(1, 'Senha é obrigatória')
  .min(6, 'Senha deve ter pelo menos 6 caracteres')
  .max(100, 'Senha deve ter no máximo 100 caracteres');

/**
 * Schema para validação de nome de usuário
 */
export const usernameSchema = z
  .string()
  .min(1, 'Nome de usuário é obrigatório')
  .min(3, 'Nome de usuário deve ter pelo menos 3 caracteres')
  .max(50, 'Nome de usuário deve ter no máximo 50 caracteres')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Nome de usuário deve conter apenas letras, números, _ e -');

/**
 * Schema para login
 */
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Senha é obrigatória'),
});

/**
 * Schema para cadastro de usuário
 */
export const registerUserSchema = z.object({
  nomeUsuario: usernameSchema,
  senha: passwordSchema,
  confirmarSenha: z.string().min(1, 'Confirmação de senha é obrigatória'),
}).refine((data) => data.senha === data.confirmarSenha, {
  message: 'Senhas não coincidem',
  path: ['confirmarSenha'],
});

/**
 * Schema para alteração de senha
 */
export const changePasswordSchema = z.object({
  senhaAtual: z.string().min(1, 'Senha atual é obrigatória'),
  novaSenha: passwordSchema,
  confirmarNovaSenha: z.string().min(1, 'Confirmação da nova senha é obrigatória'),
}).refine((data) => data.novaSenha === data.confirmarNovaSenha, {
  message: 'Senhas não coincidem',
  path: ['confirmarNovaSenha'],
});

/**
 * Schema para recuperação de senha
 */
export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

/**
 * Schema para redefinição de senha
 */
export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Token é obrigatório'),
  novaSenha: passwordSchema,
  confirmarNovaSenha: z.string().min(1, 'Confirmação da nova senha é obrigatória'),
}).refine((data) => data.novaSenha === data.confirmarNovaSenha, {
  message: 'Senhas não coincidem',
  path: ['confirmarNovaSenha'],
});

// Types derivados dos schemas
export type LoginData = z.infer<typeof loginSchema>;
export type RegisterUserData = z.infer<typeof registerUserSchema>;
export type ChangePasswordData = z.infer<typeof changePasswordSchema>;
export type ForgotPasswordData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordData = z.infer<typeof resetPasswordSchema>;
