# Regra do Augment: Uso do Context7 MCP Server

## Comando de Ativação
**Comando:** `use context7`

## Descrição
Quando o usuário mencionar "use context7" ou solicitar informações sobre bibliotecas/frameworks específicos, o Augment deve utilizar o MCP Server do Context7 para buscar documentação atualizada e precisa.

## Quando Usar
- Usuário menciona explicitamente "use context7"
- Perguntas sobre APIs, métodos ou funcionalidades de bibliotecas específicas
- Necessidade de documentação atualizada de frameworks/bibliotecas
- Implementação de funcionalidades que requerem conhecimento específico de uma biblioteca
- Troubleshooting de problemas relacionados a bibliotecas externas

## Fluxo de Trabalho

### 1. Identificação da Biblioteca
- Primeiro, use `resolve-library-id` para encontrar o ID correto da biblioteca no Context7
- Analise a query do usuário para identificar qual biblioteca/framework está sendo referenciado
- Selecione a biblioteca mais relevante baseada em:
  - Similaridade do nome
  - Relevância da descrição
  - Cobertura da documentação
  - Trust score (priorizar bibliotecas com score 7-10)

### 2. Busca da Documentação
- Use `get-library-docs` com o ID obtido do Context7
- Especifique tópicos relevantes quando possível (ex: 'hooks', 'routing', 'authentication')
- Ajuste o número de tokens conforme necessário (padrão: 10000)

### 3. Aplicação das Informações
- Utilize a documentação obtida para fornecer respostas precisas e atualizadas
- Combine com o conhecimento do codebase local quando relevante
- Priorize exemplos práticos e implementações reais

## Exemplos de Uso

### Exemplo 1: Consulta sobre React
```
Usuário: "use context7 - como implementar useEffect no React?"
```
**Ação:** 
1. `resolve-library-id` com "React"
2. `get-library-docs` com tópico "hooks"
3. Fornecer resposta baseada na documentação oficial

### Exemplo 2: Consulta sobre Next.js
```
Usuário: "use context7 - configuração de rotas dinâmicas no Next.js"
```
**Ação:**
1. `resolve-library-id` com "Next.js"
2. `get-library-docs` com tópico "routing"
3. Explicar implementação com exemplos atualizados

### Exemplo 3: Consulta sobre biblioteca específica
```
Usuário: "use context7 - como usar Prisma para queries complexas?"
```
**Ação:**
1. `resolve-library-id` com "Prisma"
2. `get-library-docs` com tópico "queries"
3. Mostrar exemplos práticos de queries complexas

## Benefícios
- **Documentação Atualizada:** Acesso às versões mais recentes das bibliotecas
- **Precisão:** Informações oficiais e confiáveis
- **Contexto Específico:** Documentação focada no tópico solicitado
- **Eficiência:** Respostas mais rápidas e precisas

## Notas Importantes
- Sempre combine as informações do Context7 com o contexto do projeto atual
- Verifique se a versão da biblioteca no projeto é compatível com a documentação
- Use o Context7 como fonte primária para documentação oficial
- Mantenha o foco na solução do problema específico do usuário

## Integração com Outras Ferramentas
- Combine com `codebase-retrieval` para entender o contexto atual do projeto
- Use junto com ferramentas de edição para implementar soluções
- Integre com ferramentas de teste para validar implementações

---

**Lembre-se:** O Context7 é uma ferramenta poderosa para obter documentação precisa e atualizada. Use-a sempre que precisar de informações específicas sobre bibliotecas e frameworks para fornecer as melhores soluções aos usuários.
