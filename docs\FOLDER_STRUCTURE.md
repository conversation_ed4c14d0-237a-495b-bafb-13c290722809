# Estrutura de Pastas do Projeto - Guia Técnico Completo

Este documento descreve a estrutura de pastas padronizada do projeto Next.js, seguin<PERSON> as melhores práticas para organização e escalabilidade. Além da estrutura, este guia fornece explicações técnicas profundas sobre cada conceito fundamental do React/Next.js, ajudando desenvolvedores a entender não apenas **onde** colocar o código, mas **por que** essa organização é importante e **como** implementar cada conceito corretamente.

## 📁 Estrutura Geral

``` 
src/
├── app/                    # App Router do Next.js 13+
│   ├── cadastro/          # Páginas específicas
│   ├── layout.tsx         # Layout raiz
│   ├── page.tsx          # Página inicial
│   └── favicon.ico       # Favicon
├── components/            # Componentes React
│   ├── ui/               # Componentes básicos de UI 
│   ├── shared/           # Componentes reutilizáveis
│   ├── forms/            # Componentes de formulário
│   ├── layout/           # Componentes de layout
│   ├── pages/            # Componentes específicos de páginas
│   └── index.ts          # Barrel export
├── contexts/             # React Contexts e Providers
├── hooks/                # Custom React Hooks
│   ├── api/             # Hooks para chamadas de API
│   ├── form/            # Hooks para formulários
│   └── index.ts         # Barrel export
├── utils/                # Funções utilitárias
├── services/             # Serviços e integrações
│   ├── api/             # Serviços de API
│   └── index.ts         # Barrel export
├── types/                # Definições de tipos TypeScript
│   ├── api/             # Tipos para APIs
│   ├── forms/           # Tipos para formulários
│   └── index.ts         # Barrel export
├── styles/               # Arquivos de estilo
│   ├── globals/         # Estilos globais
│   ├── components/      # Estilos de componentes
│   ├── utils/           # Utilitários de estilo
│   └── index.ts         # Barrel export
├── lib/                  # Bibliotecas e configurações
│   ├── formatters/      # Formatadores e máscaras
│   ├── validations/     # Funções de validação
│   └── index.ts         # Barrel export
└── constants/            # Constantes da aplicação
    └── index.ts          # Barrel export
```

## 📋 Descrição Técnica Detalhada das Pastas

### `/app` - App Router (Next.js 13+)

#### 🔍 **Definição Técnica**
O App Router é o novo sistema de roteamento do Next.js 13+ que utiliza o sistema de arquivos para definir rotas. Cada pasta representa um segmento de rota, e arquivos especiais como `page.tsx`, `layout.tsx`, `loading.tsx` definem o comportamento da rota.

#### 🎯 **Propósito e Responsabilidades**
- **Roteamento baseado em arquivos**: Estrutura de pastas define automaticamente as rotas
- **Layouts aninhados**: Permite layouts compartilhados entre páginas
- **Streaming e Suspense**: Suporte nativo para carregamento progressivo
- **Server Components**: Renderização no servidor por padrão

#### 💡 **Exemplos Práticos**
```typescript
// app/layout.tsx - Layout raiz
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt-BR">
      <body>
        <AuthProvider>
          <ThemeProvider>
            {children}
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  );
}

// app/cadastro/page.tsx - Página de cadastro
export default function CadastroPage() {
  return <CadastroForm />;
}

// app/cadastro/loading.tsx - Loading state
export default function Loading() {
  return <div>Carregando...</div>;
}
```

#### ✅ **Boas Práticas**
- Use Server Components por padrão, Client Components apenas quando necessário
- Implemente loading states para melhor UX
- Utilize layouts para código compartilhado
- Organize rotas em grupos usando `(grupo)` para organização sem afetar a URL

#### 🔗 **Relacionamentos**
- Consome componentes de `/components`
- Utiliza contexts de `/contexts`
- Importa tipos de `/types`
- Chama serviços de `/services`

### `/components` - Hierarquia de Componentes React

#### 🔍 **Definição Técnica**
Componentes React são funções ou classes que retornam elementos JSX. A hierarquia de componentes organiza-os por nível de abstração e reutilização, seguindo o princípio da responsabilidade única e composição.

#### 🎯 **Por que Separar em Hierarquia?**
- **Reutilização**: Componentes mais básicos podem ser reutilizados em contextos diferentes
- **Manutenibilidade**: Facilita localização e modificação
- **Testabilidade**: Componentes menores são mais fáceis de testar
- **Composição**: Permite construir interfaces complexas a partir de peças simples

#### `/components/ui` - Componentes Básicos de Interface

##### 🔍 **Definição Técnica**
Componentes de UI são os blocos de construção mais básicos da interface. São "dumb components" (componentes burros) que recebem props e renderizam elementos visuais sem lógica de negócio.

##### 🎯 **Propósito e Responsabilidades**
- Renderização visual pura
- Recebem dados via props
- Não fazem chamadas de API
- Não gerenciam estado de negócio
- Altamente reutilizáveis

##### 💡 **Exemplos Práticos**
```typescript
// components/ui/Button.tsx
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}

export function Button({
  variant = 'primary',
  size = 'md',
  disabled = false,
  onClick,
  children
}: ButtonProps) {
  const baseClasses = 'font-medium rounded-md transition-colors';
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',
    danger: 'bg-red-600 text-white hover:bg-red-700'
  };
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </button>
  );
}

// components/ui/Input.tsx
interface InputProps {
  type?: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
}

export function Input({
  type = 'text',
  placeholder,
  value,
  onChange,
  error,
  disabled
}: InputProps) {
  return (
    <div className="flex flex-col">
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className={`px-3 py-2 border rounded-md ${
          error ? 'border-red-500' : 'border-gray-300'
        }`}
      />
      {error && <span className="text-red-500 text-sm mt-1">{error}</span>}
    </div>
  );
}
```

##### ✅ **Boas Práticas**
- Use TypeScript interfaces para props bem definidas
- Implemente variants para diferentes estilos
- Mantenha componentes pequenos e focados
- Use composition ao invés de herança
- Documente props com JSDoc

#### `/components/shared` - Componentes Reutilizáveis

##### 🔍 **Definição Técnica**
Componentes shared são componentes de nível médio que combinam componentes UI básicos e podem ter alguma lógica interna, mas ainda são reutilizáveis em diferentes contextos.

##### 🎯 **Propósito e Responsabilidades**
- Combinam múltiplos componentes UI
- Podem ter estado interno simples
- Reutilizáveis em múltiplas páginas
- Encapsulam lógica de apresentação

##### 💡 **Exemplos Práticos**
```typescript
// components/shared/SearchBar.tsx
interface SearchBarProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  loading?: boolean;
}

export function SearchBar({ placeholder, onSearch, loading }: SearchBarProps) {
  const [query, setQuery] = useState('');
  const [debouncedQuery] = useDebounce(query, 300);

  useEffect(() => {
    if (debouncedQuery) {
      onSearch(debouncedQuery);
    }
  }, [debouncedQuery, onSearch]);

  return (
    <div className="relative">
      <Input
        type="search"
        placeholder={placeholder}
        value={query}
        onChange={setQuery}
      />
      {loading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <Spinner size="sm" />
        </div>
      )}
    </div>
  );
}

// components/shared/UserAvatar.tsx
interface UserAvatarProps {
  user: {
    name: string;
    avatar?: string;
    email: string;
  };
  size?: 'sm' | 'md' | 'lg';
  showName?: boolean;
}

export function UserAvatar({ user, size = 'md', showName }: UserAvatarProps) {
  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-12 h-12 text-base',
    lg: 'w-16 h-16 text-lg'
  };

  const initials = user.name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <div className="flex items-center space-x-3">
      <div className={`${sizeClasses[size]} rounded-full bg-blue-500 text-white flex items-center justify-center font-medium`}>
        {user.avatar ? (
          <img src={user.avatar} alt={user.name} className="w-full h-full rounded-full object-cover" />
        ) : (
          initials
        )}
      </div>
      {showName && (
        <div>
          <p className="font-medium text-gray-900">{user.name}</p>
          <p className="text-sm text-gray-500">{user.email}</p>
        </div>
      )}
    </div>
  );
}
```

#### `/components/forms` - Componentes de Formulário

##### 🔍 **Definição Técnica**
Componentes de formulário são especializados em captura e validação de dados do usuário. Eles encapsulam lógica de validação, formatação e estado de formulários.

##### 💡 **Exemplos Práticos**
```typescript
// components/forms/FormField.tsx
interface FormFieldProps {
  label: string;
  name: string;
  type?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  required?: boolean;
  placeholder?: string;
}

export function FormField({ label, name, type = 'text', value, onChange, error, required, placeholder }: FormFieldProps) {
  return (
    <div className="flex flex-col space-y-1">
      <label htmlFor={name} className="text-sm font-medium text-gray-700">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        id={name}
        name={name}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className={`px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
          error ? 'border-red-500' : 'border-gray-300'
        }`}
      />
      {error && <span className="text-red-500 text-xs">{error}</span>}
    </div>
  );
}
```

#### `/components/layout` - Componentes de Layout

##### 🔍 **Definição Técnica**
Componentes de layout definem a estrutura visual da aplicação, organizando o espaço da tela e fornecendo navegação consistente.

##### 💡 **Exemplos Práticos**
```typescript
// components/layout/Header.tsx
export function Header() {
  const { user, logout } = useAuth();

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Logo />
          <nav className="flex items-center space-x-4">
            <UserAvatar user={user} />
            <Button variant="secondary" onClick={logout}>
              Sair
            </Button>
          </nav>
        </div>
      </div>
    </header>
  );
}
```

#### `/components/pages` - Componentes Específicos de Páginas

##### 🔍 **Definição Técnica**
Componentes de página são específicos para uma única página ou funcionalidade, combinando múltiplos componentes para criar uma interface completa.

##### 🎯 **Quando Usar**
- Lógica específica de uma página
- Combinação complexa de componentes
- Estado específico da página
- Não será reutilizado em outras páginas

### `/contexts` - React Contexts

#### 🔍 **Definição Técnica**
React Context é uma API que permite compartilhar dados entre componentes sem precisar passar props manualmente através de cada nível da árvore de componentes. É uma solução para o "prop drilling" problem.

#### 🎯 **Propósito e Responsabilidades**
- **Gerenciamento de estado global**: Dados que múltiplos componentes precisam acessar
- **Evitar prop drilling**: Não precisar passar props através de múltiplos níveis
- **Temas e configurações**: Dados que afetam toda a aplicação
- **Autenticação**: Estado do usuário logado

#### 💡 **Context vs Redux - Quando Usar Cada Um**

**Use Context quando:**
- Estado simples e não muito frequente
- Poucos componentes precisam do estado
- Não precisa de time travel debugging
- Aplicação pequena/média

**Use Redux quando:**
- Estado complexo com muitas atualizações
- Precisa de debugging avançado
- Múltiplos desenvolvedores
- Aplicação grande com muitos estados

#### 💡 **Exemplos Práticos**
```typescript
// contexts/AuthContext.tsx
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      const response = await authService.login(email, password);
      setUser(response.user);
      localStorage.setItem('token', response.token);
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('token');
  };

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      authService.validateToken(token)
        .then(user => setUser(user))
        .catch(() => logout())
        .finally(() => setLoading(false));
    } else {
      setLoading(false);
    }
  }, []);

  return (
    <AuthContext.Provider value={{ user, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook customizado para usar o contexto
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// contexts/ThemeContext.tsx
type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>('light');

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  useEffect(() => {
    document.documentElement.classList.toggle('dark', theme === 'dark');
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
```

#### ✅ **Boas Práticas**
- Sempre crie hooks customizados para acessar o contexto
- Valide se o contexto está sendo usado dentro do Provider
- Mantenha contextos focados (um contexto por responsabilidade)
- Use múltiplos contextos ao invés de um contexto gigante
- Considere performance - contextos re-renderizam todos os consumidores

#### 🔗 **Relacionamentos**
- Consumido por componentes via hooks customizados
- Pode usar serviços de `/services`
- Define tipos em `/types`
- Utiliza hooks de `/hooks`

### `/hooks` - Custom Hooks

#### 🔍 **Definição Técnica**
Custom Hooks são funções JavaScript que começam com "use" e podem chamar outros hooks. Eles permitem extrair lógica de componentes para funções reutilizáveis, seguindo as regras dos hooks do React.

#### 🎯 **Diferença entre Custom Hooks e Hooks Nativos**

**Hooks Nativos do React:**
- `useState`, `useEffect`, `useContext`, `useReducer`, etc.
- Fornecidos pelo React
- Funcionalidades básicas de estado e efeitos

**Custom Hooks:**
- Criados pelo desenvolvedor
- Combinam hooks nativos
- Encapsulam lógica específica da aplicação
- Promovem reutilização de código

#### 🎯 **Propósito e Responsabilidades**
- **Reutilização de lógica**: Compartilhar lógica entre componentes
- **Separação de responsabilidades**: Manter componentes focados na UI
- **Testabilidade**: Lógica isolada é mais fácil de testar
- **Abstração**: Esconder complexidade de implementação

#### `/hooks/api` - Hooks para APIs

##### 🔍 **Definição Técnica**
Hooks de API encapsulam a lógica de chamadas HTTP, gerenciamento de estado de loading, error handling e cache de dados.

##### 💡 **Exemplos Práticos**
```typescript
// hooks/api/useFetch.ts
interface UseFetchResult<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useFetch<T>(url: string): UseFetchResult<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [url]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// hooks/api/useUsers.ts
interface User {
  id: string;
  name: string;
  email: string;
}

export function useUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const data = await userService.getAll();
      setUsers(data);
    } catch (err) {
      setError('Erro ao carregar usuários');
    } finally {
      setLoading(false);
    }
  };

  const createUser = async (userData: Omit<User, 'id'>) => {
    try {
      const newUser = await userService.create(userData);
      setUsers(prev => [...prev, newUser]);
      return newUser;
    } catch (err) {
      setError('Erro ao criar usuário');
      throw err;
    }
  };

  const updateUser = async (id: string, userData: Partial<User>) => {
    try {
      const updatedUser = await userService.update(id, userData);
      setUsers(prev => prev.map(user =>
        user.id === id ? updatedUser : user
      ));
      return updatedUser;
    } catch (err) {
      setError('Erro ao atualizar usuário');
      throw err;
    }
  };

  const deleteUser = async (id: string) => {
    try {
      await userService.delete(id);
      setUsers(prev => prev.filter(user => user.id !== id));
    } catch (err) {
      setError('Erro ao deletar usuário');
      throw err;
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  return {
    users,
    loading,
    error,
    refetch: fetchUsers,
    createUser,
    updateUser,
    deleteUser
  };
}
```

#### `/hooks/form` - Hooks para Formulários

##### 🔍 **Definição Técnica**
Hooks de formulário gerenciam estado de campos, validação, submissão e feedback de erro, abstraindo a complexidade do gerenciamento de formulários.

##### 💡 **Exemplos Práticos**
```typescript
// hooks/form/useForm.ts
interface UseFormOptions<T> {
  initialValues: T;
  validationSchema?: (values: T) => Record<keyof T, string>;
  onSubmit: (values: T) => Promise<void> | void;
}

interface UseFormReturn<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isSubmitting: boolean;
  handleChange: (field: keyof T) => (value: any) => void;
  handleBlur: (field: keyof T) => () => void;
  handleSubmit: (e: React.FormEvent) => void;
  setFieldValue: (field: keyof T, value: any) => void;
  setFieldError: (field: keyof T, error: string) => void;
  resetForm: () => void;
}

export function useForm<T extends Record<string, any>>({
  initialValues,
  validationSchema,
  onSubmit
}: UseFormOptions<T>): UseFormReturn<T> {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateField = (field: keyof T, value: any) => {
    if (validationSchema) {
      const fieldErrors = validationSchema({ ...values, [field]: value });
      setErrors(prev => ({ ...prev, [field]: fieldErrors[field] }));
    }
  };

  const handleChange = (field: keyof T) => (value: any) => {
    setValues(prev => ({ ...prev, [field]: value }));
    if (touched[field]) {
      validateField(field, value);
    }
  };

  const handleBlur = (field: keyof T) => () => {
    setTouched(prev => ({ ...prev, [field]: true }));
    validateField(field, values[field]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validar todos os campos
    if (validationSchema) {
      const allErrors = validationSchema(values);
      setErrors(allErrors);

      const hasErrors = Object.values(allErrors).some(error => error);
      if (hasErrors) {
        setIsSubmitting(false);
        return;
      }
    }

    try {
      await onSubmit(values);
    } catch (error) {
      console.error('Erro no submit:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const setFieldValue = (field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }));
  };

  const setFieldError = (field: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [field]: error }));
  };

  const resetForm = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  };

  return {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldError,
    resetForm
  };
}

// hooks/form/useValidation.ts
export function useValidation() {
  const validateEmail = (email: string): string => {
    if (!email) return 'Email é obrigatório';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return 'Email inválido';
    return '';
  };

  const validateCPF = (cpf: string): string => {
    if (!cpf) return 'CPF é obrigatório';
    const cleanCPF = cpf.replace(/\D/g, '');
    if (cleanCPF.length !== 11) return 'CPF deve ter 11 dígitos';
    // Lógica de validação de CPF...
    return '';
  };

  const validateRequired = (value: any, fieldName: string): string => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      return `${fieldName} é obrigatório`;
    }
    return '';
  };

  return {
    validateEmail,
    validateCPF,
    validateRequired
  };
}
```

#### ✅ **Boas Práticas para Hooks**
- Sempre comece o nome com "use"
- Mantenha hooks focados em uma responsabilidade
- Use TypeScript para tipagem forte
- Implemente error handling adequado
- Considere performance com `useCallback` e `useMemo`
- Teste hooks isoladamente

#### 🔗 **Relacionamentos**
- Usados por componentes para lógica reutilizável
- Podem consumir contexts
- Utilizam serviços de `/services`
- Definem tipos em `/types`

### `/utils` vs `/lib` - Diferença Fundamental

#### 🔍 **Definição Técnica**

**`/utils` - Funções Utilitárias:**
- Funções puras sem dependências externas
- Lógica de negócio simples
- Helpers gerais da aplicação
- Não dependem do React ou outras bibliotecas

**`/lib` - Bibliotecas Internas:**
- Configurações de bibliotecas externas
- Wrappers e adaptadores
- Formatadores e validadores específicos
- Podem ter dependências externas

#### 💡 **Exemplos Práticos**

```typescript
// utils/index.ts - Funções utilitárias puras
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export function formatDate(date: Date, format: string = 'dd/MM/yyyy'): string {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();

  return format
    .replace('dd', day)
    .replace('MM', month)
    .replace('yyyy', year.toString());
}

export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

// utils/formData.ts - Utilitários específicos para formulários
export const generos = [
  { value: 'masculino', label: 'Masculino' },
  { value: 'feminino', label: 'Feminino' },
  { value: 'outro', label: 'Outro' }
];

export const estados = [
  { value: 'SP', label: 'São Paulo' },
  { value: 'RJ', label: 'Rio de Janeiro' },
  // ... outros estados
];
```

### `/services` - Camada de Abstração para APIs

#### 🔍 **Definição Técnica**
Services são classes ou módulos que encapsulam a lógica de comunicação com APIs externas, bancos de dados ou outros serviços. Eles fornecem uma interface consistente para operações de dados.

#### 🎯 **Propósito e Responsabilidades**
- **Abstração de APIs**: Esconder detalhes de implementação de chamadas HTTP
- **Centralização**: Um local para todas as operações de uma entidade
- **Reutilização**: Métodos que podem ser usados por múltiplos componentes
- **Error Handling**: Tratamento consistente de erros
- **Transformação de dados**: Converter dados entre formatos

#### 🎯 **Padrões de Implementação**

##### **Padrão 1: Service Classes**
```typescript
// services/api/userService.ts
class UserService {
  private baseURL = '/api/users';

  async getAll(): Promise<User[]> {
    const response = await apiClient.get<User[]>(this.baseURL);
    return response.data;
  }

  async getById(id: string): Promise<User> {
    const response = await apiClient.get<User>(`${this.baseURL}/${id}`);
    return response.data;
  }

  async create(userData: CreateUserData): Promise<User> {
    const response = await apiClient.post<User>(this.baseURL, userData);
    return response.data;
  }

  async update(id: string, userData: UpdateUserData): Promise<User> {
    const response = await apiClient.put<User>(`${this.baseURL}/${id}`, userData);
    return response.data;
  }

  async delete(id: string): Promise<void> {
    await apiClient.delete(`${this.baseURL}/${id}`);
  }
}

export const userService = new UserService();
```

##### **Padrão 2: Functional Services**
```typescript
// services/api/authService.ts
interface LoginCredentials {
  email: string;
  password: string;
}

interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export const authService = {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);
    return response.data;
  },

  async logout(): Promise<void> {
    await apiClient.post('/auth/logout');
  },

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/refresh', {
      refreshToken
    });
    return response.data;
  },

  async validateToken(token: string): Promise<User> {
    const response = await apiClient.get<User>('/auth/validate', {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  }
};
```

##### **Configuração do Cliente API**
```typescript
// services/api/apiClient.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor para adicionar token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor para tratar erros
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expirado, tentar refresh
          const refreshToken = localStorage.getItem('refreshToken');
          if (refreshToken) {
            try {
              const response = await this.client.post('/auth/refresh', {
                refreshToken
              });
              const { token } = response.data;
              localStorage.setItem('token', token);

              // Repetir a requisição original
              error.config.headers.Authorization = `Bearer ${token}`;
              return this.client.request(error.config);
            } catch (refreshError) {
              // Refresh falhou, redirecionar para login
              localStorage.removeItem('token');
              localStorage.removeItem('refreshToken');
              window.location.href = '/login';
            }
          }
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig) {
    return this.client.get<T>(url, config);
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig) {
    return this.client.post<T>(url, data, config);
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig) {
    return this.client.put<T>(url, data, config);
  }

  async delete<T>(url: string, config?: AxiosRequestConfig) {
    return this.client.delete<T>(url, config);
  }
}

export const apiClient = new ApiClient();
```

#### ✅ **Boas Práticas para Services**
- Use TypeScript para tipagem forte de requests/responses
- Implemente error handling consistente
- Configure interceptors para autenticação automática
- Use environment variables para URLs
- Implemente retry logic para requests críticos
- Adicione logging para debugging
- Considere cache para dados que não mudam frequentemente

#### 🔗 **Relacionamentos**
- Consumidos por hooks de `/hooks/api`
- Usam tipos de `/types/api`
- Podem usar utilitários de `/utils`
- Configurados com constantes de `/constants`

### `/types` - Definições de Tipos TypeScript

#### 🔍 **Definição Técnica**
Types em TypeScript definem a estrutura e formato dos dados na aplicação. Eles fornecem type safety, melhor IntelliSense e documentação viva do código.

#### 🎯 **Interface vs Type - Quando Usar Cada Um**

**Use `interface` quando:**
- Definindo contratos de objetos
- Precisar de extensão/herança
- Definindo APIs públicas
- Trabalhando com classes

**Use `type` quando:**
- Criando unions ou intersections
- Definindo tipos primitivos customizados
- Criando aliases para tipos complexos
- Trabalhando com tipos condicionais

#### 💡 **Convenções de Nomenclatura**

```typescript
// ✅ Boas práticas de nomenclatura
interface User {          // PascalCase para interfaces
  id: string;
  name: string;
}

type UserRole = 'admin' | 'user' | 'guest';  // PascalCase para types
type ApiResponse<T> = {   // Generics com T, U, V...
  data: T;
  success: boolean;
};

// Sufixos descritivos
interface CreateUserRequest { }    // Request para criação
interface UpdateUserRequest { }    // Request para atualização
interface UserResponse { }         // Response da API
interface UserFormData { }         // Dados do formulário
```

#### `/types/api` - Tipos para APIs

##### 💡 **Exemplos Práticos**
```typescript
// types/api/user.ts
export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 'admin' | 'moderator' | 'user';

export interface CreateUserRequest {
  name: string;
  email: string;
  password: string;
  role?: UserRole;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  role?: UserRole;
  avatar?: string;
}

export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
}

// types/api/auth.ts
export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// types/api/common.ts
export interface ApiError {
  message: string;
  code: string;
  details?: Record<string, any>;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: ApiError[];
}

// Utility types para APIs
export type ApiEndpoint = 'users' | 'auth' | 'posts' | 'comments';

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export interface RequestConfig {
  method: HttpMethod;
  endpoint: string;
  params?: Record<string, any>;
  data?: any;
  headers?: Record<string, string>;
}
```

#### `/types/forms` - Tipos para Formulários

##### 💡 **Exemplos Práticos**
```typescript
// types/forms/validation.ts
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface FieldValidation {
  [fieldName: string]: ValidationRule;
}

export interface ValidationErrors {
  [fieldName: string]: string;
}

export interface FormState<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isSubmitting: boolean;
  isValid: boolean;
}

// types/forms/fields.ts
export interface BaseFieldProps {
  name: string;
  label: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  helperText?: string;
}

export interface InputFieldProps extends BaseFieldProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel';
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
}

export interface SelectFieldProps extends BaseFieldProps {
  value: string;
  onChange: (value: string) => void;
  options: SelectOption[];
  multiple?: boolean;
}

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface CheckboxFieldProps extends BaseFieldProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
}

// types/forms/cadastro.ts - Exemplo específico
export interface CadastroFormData {
  // Dados pessoais
  nome: string;
  email: string;
  cpf: string;
  telefone: string;
  dataNascimento: string;
  genero: string;

  // Endereço
  cep: string;
  endereco: string;
  numero: string;
  complemento?: string;
  bairro: string;
  cidade: string;
  estado: string;

  // Informações adicionais
  escolaridade: string;
  renda: string;
  profissao?: string;

  // Termos
  aceitaTermos: boolean;
  aceitaNewsletter: boolean;
}

export interface CadastroFormErrors {
  [K in keyof CadastroFormData]?: string;
}

export interface CadastroFormTouched {
  [K in keyof CadastroFormData]?: boolean;
}

// Utility types para formulários
export type FormFieldType = 'text' | 'email' | 'password' | 'select' | 'checkbox' | 'radio' | 'textarea' | 'masked';

export interface FormFieldConfig {
  type: FormFieldType;
  label: string;
  placeholder?: string;
  required?: boolean;
  validation?: ValidationRule;
  options?: SelectOption[];  // Para select/radio
  mask?: string;            // Para campos com máscara
}

export type FormSchema<T> = {
  [K in keyof T]: FormFieldConfig;
};
```

#### 💡 **Tipos Avançados e Utility Types**

```typescript
// types/utils.ts - Utility types customizados
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type NonEmptyArray<T> = [T, ...T[]];

export type ValueOf<T> = T[keyof T];

export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

// Exemplo de uso
interface ExampleForm {
  name: string;
  email: string;
  age?: number;
  isActive: boolean;
}

// Torna 'name' e 'email' opcionais, mantém o resto
type PartialForm = Optional<ExampleForm, 'name' | 'email'>;

// Torna 'age' obrigatório
type FormWithRequiredAge = RequiredFields<ExampleForm, 'age'>;

// Pega apenas as chaves que são strings
type StringKeys = KeysOfType<ExampleForm, string>; // 'name' | 'email'
```

#### ✅ **Boas Práticas para Types**
- Use nomes descritivos e consistentes
- Prefira `interface` para objetos, `type` para unions
- Organize por domínio/funcionalidade
- Use generics para reutilização
- Documente tipos complexos com JSDoc
- Evite `any`, use `unknown` quando necessário
- Use utility types do TypeScript quando apropriado

#### 🔗 **Relacionamentos**
- Usados por todos os outros módulos para type safety
- Definem contratos entre services e components
- Utilizados por hooks para tipagem de estado
- Importados por contexts para tipagem de dados

### `/styles` - Estilos
#### `/styles/globals`
- **Propósito**: Estilos globais da aplicação
- **Conteúdo**: Reset CSS, variáveis globais, Tailwind imports

#### `/styles/components`
- **Propósito**: Estilos específicos de componentes (quando necessário)

#### `/styles/utils`
- **Propósito**: Classes utilitárias customizadas

### `/lib` - Bibliotecas
#### `/lib/formatters`
- **Propósito**: Formatadores de dados (CPF, telefone, moeda)
- **Exemplo**: `applyCpfMask`, `formatCurrency`

#### `/lib/validations`
- **Propósito**: Funções de validação
- **Exemplo**: `validateCPF`, `validateEmail`

### `/constants` - Constantes
- **Propósito**: Valores constantes da aplicação
- **Exemplo**: `API_URLS`, `ROUTES`, `VALIDATION_MESSAGES`

## 🔧 Configuração de Imports

### Path Aliases (tsconfig.json)
```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./src/*"],
      "@/components": ["./src/components"],
      "@/hooks": ["./src/hooks"],
      "@/utils": ["./src/utils"],
      "@/services": ["./src/services"],
      "@/types": ["./src/types"],
      "@/lib": ["./src/lib"],
      "@/styles": ["./src/styles"],
      "@/constants": ["./src/constants"]
    }
  }
}
```

### Barrel Exports
Cada pasta principal possui um arquivo `index.ts` que exporta todos os módulos:

```typescript
// src/components/index.ts
export * from './forms';
export * from './shared';
export * from './ui';
export * from './layout';
export * from './pages';
```

## 📝 Exemplos de Uso

### Importando Componentes
```typescript
// ✅ Recomendado - usando barrel exports
import { FormField, FormSelect } from '@/components/forms';
import { Logo } from '@/components/shared';

// ✅ Alternativo - import direto
import { FormField } from '@/components/forms/FormField';
```

### Importando Utilitários
```typescript
// ✅ Usando path alias
import { applyCpfMask } from '@/lib/formatters';
import { validateEmail } from '@/lib/validations';
import { API_URLS } from '@/constants';
```

## 🎯 Benefícios desta Estrutura

1. **Escalabilidade**: Fácil adição de novos componentes e funcionalidades
2. **Manutenibilidade**: Código organizado e fácil de localizar
3. **Reutilização**: Componentes bem categorizados para máxima reutilização
4. **Imports Limpos**: Path aliases e barrel exports facilitam imports
5. **Separação de Responsabilidades**: Cada pasta tem um propósito específico
6. **Padrão da Equipe**: Estrutura consistente para toda a equipe

## 🏗️ Arquitetura e Fluxo de Dados

### 📊 **Diagrama de Relacionamentos**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   App Router    │    │   Components    │    │    Contexts     │
│   (Pages)       │◄──►│   (UI Layer)    │◄──►│  (Global State) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Hooks       │    │     Types       │    │    Services     │
│  (Logic Layer)  │◄──►│ (Type Safety)   │◄──►│  (Data Layer)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Utils/Lib     │    │   Constants     │    │     Styles      │
│  (Utilities)    │    │ (Configuration) │    │   (Styling)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🔄 **Fluxo de Dados Típico**

1. **Página (App Router)** renderiza componentes
2. **Componentes** usam hooks para lógica
3. **Hooks** consomem contexts e services
4. **Services** fazem chamadas para APIs
5. **Types** garantem type safety em todo o fluxo
6. **Utils/Lib** fornecem funções auxiliares

### 💡 **Exemplo de Fluxo Completo**

```typescript
// 1. Página usa componente
// app/usuarios/page.tsx
export default function UsuariosPage() {
  return <UserList />;
}

// 2. Componente usa hook
// components/pages/UserList.tsx
export function UserList() {
  const { users, loading, error, createUser } = useUsers();
  const { user: currentUser } = useAuth();

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} />;

  return (
    <div>
      {users.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
    </div>
  );
}

// 3. Hook usa service
// hooks/api/useUsers.ts
export function useUsers() {
  const [users, setUsers] = useState<User[]>([]);

  const fetchUsers = async () => {
    const data = await userService.getAll();
    setUsers(data);
  };

  return { users, fetchUsers };
}

// 4. Service faz chamada API
// services/api/userService.ts
export const userService = {
  async getAll(): Promise<User[]> {
    const response = await apiClient.get<User[]>('/users');
    return response.data;
  }
};
```

## 🎯 **Princípios Arquiteturais**

### 1. **Separação de Responsabilidades**
- **UI**: Apenas renderização e interação
- **Lógica**: Isolada em hooks e services
- **Estado**: Gerenciado em contexts
- **Dados**: Abstraídos em services

### 2. **Inversão de Dependência**
- Componentes dependem de abstrações (hooks)
- Hooks dependem de abstrações (services)
- Services implementam interfaces definidas

### 3. **Composição sobre Herança**
- Componentes pequenos e composáveis
- Hooks reutilizáveis
- Services modulares

### 4. **Type Safety First**
- Todos os dados tipados
- Interfaces bem definidas
- Validação em tempo de compilação

## 🧪 **Estratégias de Teste**

### **Testes por Camada**

```typescript
// Testando componentes UI
// __tests__/components/ui/Button.test.tsx
import { render, fireEvent } from '@testing-library/react';
import { Button } from '@/components/ui/Button';

test('should call onClick when clicked', () => {
  const handleClick = jest.fn();
  const { getByRole } = render(
    <Button onClick={handleClick}>Click me</Button>
  );

  fireEvent.click(getByRole('button'));
  expect(handleClick).toHaveBeenCalledTimes(1);
});

// Testando hooks
// __tests__/hooks/useUsers.test.ts
import { renderHook, act } from '@testing-library/react';
import { useUsers } from '@/hooks/api/useUsers';

jest.mock('@/services/api/userService');

test('should fetch users on mount', async () => {
  const { result } = renderHook(() => useUsers());

  await act(async () => {
    await result.current.fetchUsers();
  });

  expect(result.current.users).toHaveLength(2);
});

// Testando services
// __tests__/services/userService.test.ts
import { userService } from '@/services/api/userService';
import { apiClient } from '@/services/api/apiClient';

jest.mock('@/services/api/apiClient');

test('should fetch all users', async () => {
  const mockUsers = [{ id: '1', name: 'John' }];
  (apiClient.get as jest.Mock).mockResolvedValue({ data: mockUsers });

  const users = await userService.getAll();

  expect(users).toEqual(mockUsers);
  expect(apiClient.get).toHaveBeenCalledWith('/users');
});
```

## 📚 **Guias de Implementação**

### **1. Adicionando um Novo Componente UI**
```bash
# 1. Criar o arquivo
touch src/components/ui/NewComponent.tsx

# 2. Implementar o componente
# 3. Adicionar ao barrel export
echo "export { NewComponent } from './NewComponent';" >> src/components/ui/index.ts

# 4. Criar testes
touch __tests__/components/ui/NewComponent.test.tsx
```

### **2. Criando um Novo Hook**
```bash
# 1. Criar o arquivo
touch src/hooks/useNewFeature.ts

# 2. Implementar o hook
# 3. Adicionar tipos se necessário
touch src/types/newFeature.ts

# 4. Adicionar ao barrel export
echo "export { useNewFeature } from './useNewFeature';" >> src/hooks/index.ts
```

### **3. Adicionando um Novo Service**
```bash
# 1. Criar o service
touch src/services/api/newService.ts

# 2. Definir tipos da API
touch src/types/api/newService.ts

# 3. Criar hook para o service
touch src/hooks/api/useNewService.ts

# 4. Atualizar barrel exports
```

## 🚀 **Próximos Passos Recomendados**

### **Fase 1: Fundação (Semana 1-2)**
1. **Implementar componentes UI básicos**
   - Button, Input, Card, Modal, Spinner
   - Criar Storybook para documentação

2. **Configurar autenticação**
   - AuthContext e AuthProvider
   - useAuth hook
   - authService com JWT

3. **Setup de testes**
   - Configurar Jest e Testing Library
   - Criar utilities de teste
   - Implementar CI/CD

### **Fase 2: Funcionalidades Core (Semana 3-4)**
1. **Sistema de formulários robusto**
   - useForm hook avançado
   - Validação com Zod ou Yup
   - Componentes de formulário reutilizáveis

2. **Gerenciamento de estado**
   - Contexts para diferentes domínios
   - Estado local vs global
   - Otimização de performance

3. **Integração com APIs**
   - Services completos
   - Error handling global
   - Loading states e retry logic

### **Fase 3: Otimização e Escalabilidade (Semana 5-6)**
1. **Performance**
   - Code splitting
   - Lazy loading
   - Memoização estratégica

2. **Developer Experience**
   - ESLint rules customizadas
   - Prettier configuration
   - Husky hooks

3. **Monitoramento**
   - Error tracking (Sentry)
   - Analytics
   - Performance monitoring

## 🎯 **Benefícios desta Arquitetura**

### **Para Desenvolvedores**
- **Produtividade**: Estrutura clara facilita desenvolvimento
- **Manutenibilidade**: Código organizado e previsível
- **Reutilização**: Componentes e hooks reutilizáveis
- **Type Safety**: Menos bugs em produção

### **Para o Projeto**
- **Escalabilidade**: Fácil adicionar novas funcionalidades
- **Testabilidade**: Cada camada pode ser testada isoladamente
- **Performance**: Otimizações podem ser aplicadas por camada
- **Colaboração**: Estrutura consistente para toda a equipe

### **Para o Negócio**
- **Time to Market**: Desenvolvimento mais rápido
- **Qualidade**: Menos bugs e melhor UX
- **Manutenção**: Custos reduzidos de manutenção
- **Flexibilidade**: Fácil adaptação a mudanças de requisitos
