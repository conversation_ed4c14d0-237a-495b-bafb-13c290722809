"use client";

import { <PERSON><PERSON> } from "@heroui/react";
import { Logo } from "@/components/shared";
import {
  FormField,
  FormSelect,
  FormSection,
  PersonTypeSelector,
  MaskedFormField,
} from "@/components/forms";
import { generos, estados, escolaridades, rendas } from "@/utils";
import { useForm } from "@/hooks/form/useForm";
import {
  registrationFormSchema,
  initialRegistrationValues,
  type RegistrationFormData,
} from "@/lib/validations/schemas/registration";

export default function CadastroPage() {
  // Usar o hook de formulário com validação integrada
  const {
    formData,
    errors,
    isSubmitting,
    setValue,
    handleChange,
    handleBlur,
    handleSubmit,
  } = useForm<RegistrationFormData>({
    initialValues: initialRegistrationValues as RegistrationFormData,
    validation: {
      schema: registrationFormSchema,
    },
    callbacks: {
      onSubmit: async (values: RegistrationFormData) => {
        console.log("Dados do formulário:", values);
        // Aqui seria feita a submissão para a API
        // Simular delay de submissão
        await new Promise((resolve) => setTimeout(resolve, 2000));
        alert("Cadastro realizado com sucesso!");
      },
    },
  });

  // Função personalizada para mudança de tipo de pessoa
  const handlePersonTypeChange = (type: "fisica" | "juridica") => {
    setValue("tipoPessoa", type as "fisica" | "juridica");
  };

  return (
    <div className="min-h-screen bg-[#F9FAFB]">
      {/* Header com Logo */}
      <header className="bg-white shadow-sm">
        <div className="flex justify-center items-center py-6 px-[662px]">
          <Logo />
        </div>
      </header>

      {/* Conteúdo Principal */}
      <main className="w-full">
        <div className="max-w-[1440px] mx-auto px-[336px] py-8">
          {/* Título */}
          <div className="text-center mb-5">
            <h1 className="text-2xl font-medium text-[#1B3B6F] leading-8 mb-5">
              Cadastro de Usuário
            </h1>

            {/* Caixa de informação */}
            <div className="bg-[#F5F9FF] border border-[#E0E9F7] rounded-lg p-[17px] mb-7 bg-[#F5F9FF]">
              <p className="text-base font-normal text-[#374151] leading-6">
                O preenchimento de dados não obrigatórios auxilia o Governo a
                coletar informações que serão utilizadas para melhorar o sistema
                e as políticas de transparência pública. A identificação das
                pessoas a que essas informações se referem não será divulgada.
              </p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-7">
            {/* Tipo de Pessoa */}
            <PersonTypeSelector
              value={formData.tipoPessoa || ""}
              onChange={handlePersonTypeChange}
              error={errors.tipoPessoa}
            />

            {/* Dados Pessoais */}
            <FormSection
              title="Dados Pessoais"
              icon={
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                </svg>
              }
            >
              <FormField
                label="E-mail"
                name="email"
                type="email"
                value={formData.email || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                required
                error={errors.email}
              />

              <FormField
                label="Nome completo"
                name="nomeCompleto"
                value={formData.nomeCompleto || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                required
                error={errors.nomeCompleto}
              />

              <FormField
                label="Data de nascimento"
                name="dataNascimento"
                type="date"
                value={formData.dataNascimento || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="yyyy / mm / dd"
                error={errors.dataNascimento}
              />

              <FormSelect
                label="Gênero"
                name="genero"
                value={formData.genero || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                options={generos}
                error={errors.genero}
              />

              <MaskedFormField
                label="CPF"
                name="cpf"
                value={formData.cpf || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                mask="cpf"
                error={errors.cpf}
              />

              <FormField
                label="RG"
                name="rg"
                value={formData.rg || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.rg}
              />

              <FormField
                label="Órgão expedidor"
                name="orgaoExpedidor"
                value={formData.orgaoExpedidor || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.orgaoExpedidor}
              />

              <FormSelect
                label="Renda"
                name="renda"
                value={formData.renda || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                options={rendas}
                error={errors.renda}
              />

              <FormSelect
                label="Escolaridade"
                name="escolaridade"
                value={formData.escolaridade || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                options={escolaridades}
                error={errors.escolaridade}
              />

              <MaskedFormField
                label="Telefone"
                name="telefone"
                value={formData.telefone || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                mask="phone"
                error={errors.telefone}
              />

              <MaskedFormField
                label="Celular"
                name="celular"
                value={formData.celular || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                mask="phone"
                error={errors.celular}
                className="md:col-span-2"
              />
            </FormSection>

            {/* Endereço */}
            <FormSection
              title="Endereço"
              icon={
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
                </svg>
              }
            >
              <div className="text-base font-normal text-[#4B5563] leading-6 mb-6 md:col-span-2">
                Preenchimento opcional, mas recomendado para melhorar a coleta
                de dados.
              </div>

              <MaskedFormField
                label="CEP"
                name="cep"
                value={formData.cep || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                mask="cep"
                error={errors.cep}
              />

              <FormField
                label="Cidade"
                name="cidade"
                value={formData.cidade || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.cidade}
              />

              <FormSelect
                label="Estado"
                name="estado"
                value={formData.estado || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                options={estados}
                error={errors.estado}
              />

              <FormField
                label="Logradouro"
                name="logradouro"
                value={formData.logradouro || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.logradouro}
              />

              <FormField
                label="Bairro"
                name="bairro"
                value={formData.bairro || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.bairro}
              />

              <FormField
                label="Número"
                name="numero"
                value={formData.numero || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.numero}
              />

              <FormField
                label="Complemento"
                name="complemento"
                value={formData.complemento || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                error={errors.complemento}
                className="md:col-span-2"
              />
            </FormSection>

            {/* Dados do Usuário */}
            <FormSection
              title="Dados do Usuário"
              icon={
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" />
                </svg>
              }
            >
              <FormField
                label="Nome de usuário"
                name="nomeUsuario"
                value={formData.nomeUsuario || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                required
                error={errors.nomeUsuario}
              />

              <FormField
                label="Senha"
                name="senha"
                type="password"
                value={formData.senha || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                required
                error={errors.senha}
              />

              <FormField
                label="Confirme sua senha"
                name="confirmarSenha"
                type="password"
                value={formData.confirmarSenha || ""}
                onChange={handleChange}
                onBlur={handleBlur}
                required
                error={errors.confirmarSenha}
                className="md:col-span-2"
              />

              <div className="text-sm font-normal text-[#4B5563] leading-5 md:col-span-2">
                Sua senha deve conter pelo menos 6 caracteres.
              </div>
            </FormSection>

            {/* Botão de Cadastro */}
            <div className="flex justify-center mt-[52px]">
              <Button
                type="submit"
                className="bg-[#234A95] text-white py-3 px-[346px] rounded-md hover:bg-[#1e3f7f] font-medium text-base leading-6"
                size="lg"
                isLoading={isSubmitting}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Cadastrando..." : "Cadastrar"}
              </Button>
            </div>
          </form>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-[#F3F4F6] py-6 px-[410px]">
        <div className="text-center">
          <p className="text-sm font-normal text-[#4B5563] leading-5">
            © 2024 Sistema de Ouvidorias do Estado de Sergipe - SE-OUV. Todos os
            direitos reservados.
          </p>
        </div>
      </footer>
    </div>
  );
}
